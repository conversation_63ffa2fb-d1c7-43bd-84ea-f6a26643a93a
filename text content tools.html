<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Text Content Tools – Format, Convert & Optimize Your Writing Online</title>
  <meta name="description" content="Format, convert, and optimize your content using our free text tools. Count words, generate slugs, sort or clean up text instantly — perfect for writers, bloggers, and SEO specialists." />
  <meta name="keywords" content="text tools, text utilities, word counter, lorem ipsum, case converter, text to slug, text generator, online tools, free tools" />
  <link rel="canonical" href="https://www.webtoolskit.org/p/text-contents_87.html" />

  <!-- Page-specific Open Graph Meta Tags -->
  <meta property="og:url" content="https://www.webtoolskit.org/p/text-contents_87.html" />
  <meta property="og:title" content="Text Content Tools - Free Online Text Utilities" />
  <meta property="og:description" content="Access a complete set of free online text tools. Create dummy text, count words, convert text case, generate slugs, and more with our professional text utilities." />
  <meta property="og:image" content="https://www.webtoolskit.org/images/texts-og.jpg" />

  <!-- Page-specific Twitter Card Meta Tags -->
  <meta name="twitter:url" content="https://www.webtoolskit.org/p/text-contents_87.html" />
  <meta name="twitter:title" content="Text Content Tools - Free Online Text Utilities" />
  <meta name="twitter:description" content="Access a complete set of free online text tools. Create dummy text, count words, convert text case, generate slugs, and more with our professional text utilities." />
  <meta name="twitter:image" content="https://www.webtoolskit.org/images/texts-og.jpg" />

  <!-- Enhanced Schema.org structured data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": ["WebPage", "CollectionPage"],
    "name": "Text Content Tools – Format, Convert & Optimize Your Writing Online",
    "description": "Format, convert, and optimize your content using our free text tools. Count words, generate slugs, sort or clean up text instantly — perfect for writers, bloggers, and SEO specialists.",
    "url": "https://www.webtoolskit.org/p/text-contents_87.html",
    "isAccessibleForFree": true,
    "datePublished": "2025-06-15",
    "dateModified": "2025-06-18",
    "author": {
      "@type": "Organization",
      "name": "WebToolsKit",
      "url": "https://www.webtoolskit.org",
      "logo": {
        "@type": "ImageObject",
        "url": "https://www.webtoolskit.org/images/logo.png"
      }
    },
    "publisher": {
      "@type": "Organization",
      "name": "WebToolsKit",
      "url": "https://www.webtoolskit.org"
    },
    "mainEntity": {
      "@type": "ItemList",
      "name": "Text Content Tools Collection",
      "description": "Comprehensive collection of free online text tools",
      "numberOfItems": 12,
      "itemListElement": [
        {
          "@type": "WebApplication",
          "position": 1,
          "name": "Text to Slug Converter",
          "description": "Convert text into URL-friendly slug format for clean URLs and SEO optimization",
          "url": "https://www.webtoolskit.org/p/text-to-slug_30.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["URL slug generation", "SEO optimization", "Real-time conversion"]
        },
        {
          "@type": "WebApplication",
          "position": 2,
          "name": "Lorem Ipsum Generator",
          "description": "Generate placeholder text for designs, layouts, and mockups with custom options",
          "url": "https://www.webtoolskit.org/p/lorem-ipsum-generator.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["Placeholder text generation", "Custom length options", "Multiple formats"]
        },
        {
          "@type": "WebApplication",
          "position": 3,
          "name": "Case Converter",
          "description": "Convert text between uppercase, lowercase, title case, and sentence case formats",
          "url": "https://www.webtoolskit.org/p/case-converter.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["Multiple case formats", "Instant conversion", "Bulk text processing"]
        },
        {
          "@type": "WebApplication",
          "position": 4,
          "name": "Word Counter",
          "description": "Count words, characters, sentences, and paragraphs with detailed statistics",
          "url": "https://www.webtoolskit.org/p/word-counter.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["Word counting", "Character counting", "Reading time estimation", "Detailed statistics"]
        },
        {
          "@type": "WebApplication",
          "position": 5,
          "name": "Remove Line Breaks",
          "description": "Remove unwanted line breaks and formatting issues to clean up your text content",
          "url": "https://www.webtoolskit.org/p/remove-line-breaks.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["Line break removal", "Text formatting cleanup", "Bulk text processing"]
        },
        {
          "@type": "WebApplication",
          "position": 6,
          "name": "Random Word Generator",
          "description": "Generate random words for creative writing, brainstorming, passwords, and more",
          "url": "https://www.webtoolskit.org/p/random-word-generator.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["Random word generation", "Creative writing aid", "Password generation", "Brainstorming tool"]
        },
        {
          "@type": "WebApplication",
          "position": 7,
          "name": "Privacy Policy Generator",
          "description": "Create customized privacy policy for your website that complies with legal rules",
          "url": "https://www.webtoolskit.org/p/privacy-policy-generator.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["Legal compliance", "Customizable templates", "GDPR compliance", "Website protection"]
        },
        {
          "@type": "WebApplication",
          "position": 8,
          "name": "Terms And Conditions Generator",
          "description": "Generate comprehensive terms and conditions for your website, app, or service",
          "url": "https://www.webtoolskit.org/p/terms-and-condition-generator.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["Legal document generation", "Service protection", "User agreement creation", "Business compliance"]
        },
        {
          "@type": "WebApplication",
          "position": 9,
          "name": "Disclaimer Generator",
          "description": "Create professional disclaimer to protect your website or business from legal issues",
          "url": "https://www.webtoolskit.org/p/disclaimer-generator.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["Legal protection", "Business compliance", "Risk mitigation", "Professional disclaimers"]
        },
        {
          "@type": "WebApplication",
          "position": 10,
          "name": "Text Repeater",
          "description": "Repeat any text or phrase multiple times with customizable separators and options",
          "url": "https://www.webtoolskit.org/p/text-repeater.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["Text repetition", "Custom separators", "Bulk text generation", "Pattern creation"]
        },
        {
          "@type": "WebApplication",
          "position": 11,
          "name": "Text Sorter",
          "description": "Sort lines of text alphabetically, numerically, by length, or in reverse order",
          "url": "https://www.webtoolskit.org/p/text-sorter.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["Alphabetical sorting", "Numerical sorting", "Length-based sorting", "Reverse sorting"]
        },
        {
          "@type": "WebApplication",
          "position": 12,
          "name": "Comma Separator",
          "description": "Add or remove commas from lists and convert between different list data formats",
          "url": "https://www.webtoolskit.org/p/comma-separator.html",
          "applicationCategory": "UtilityApplication",
          "operatingSystem": "Any",
          "browserRequirements": "Requires JavaScript",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
          },
          "featureList": ["Comma addition", "Comma removal", "List formatting", "Data conversion"]
        }
      ]
    },
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": "https://www.webtoolskit.org"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "Text Content Tools"
        }
      ]
    }
  }
  </script>

  <style>
    /* Duplicate CSS variables and base styles removed - inherit from main template */

    /* Page-specific styles only */
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 8px 16px;
    }

    .page-header {
      text-align: center;
      margin-bottom: 20px;
    }

    .page-title {
      font-size: 36px;
      font-weight: 700;
      color: var(--primary-color);
      margin-bottom: 10px;
      line-height: 1.2;
    }

    .page-description {
      font-size: 1.1rem;
      color: var(--text-color-light);
      max-width: 600px;
      margin: 0 auto;
      line-height: 1.5;
    }

    .tools-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 16px;
      justify-items: center;
    }

    .tool-card {
      background: var(--card-bg);
      border: 1px solid var(--border-color);
      border-radius: 10px;
      padding: 18px;
      text-align: center;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 2px 8px rgba(0,0,0,0.08);
      position: relative;
      overflow: hidden;
      cursor: pointer;
    }

    .tool-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 4px;
      background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
      opacity: 0;
      transition: opacity 0.4s ease;
    }

    .tool-card::after {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      transition: left 0.6s ease;
    }

    /* SCIENTIFIC FIX: Only apply hover effects on devices that can actually hover */
    @media (hover: hover) {
      .tool-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 12px 35px rgba(0,0,0,0.2);
        border-color: var(--primary-color);
      }

      .tool-card:hover::before {
        opacity: 1;
      }

      .tool-card:hover::after {
        left: 100%;
      }
    }

    .tool-icon {
      width: 56px;
      height: 56px;
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 12px;
      font-size: 22px;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
    }

    .tool-icon::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    @media (hover: hover) {
      .tool-card:hover .tool-icon {
        transform: scale(1.1) rotate(5deg);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
      }

      .tool-card:hover .tool-icon::before {
        opacity: 1;
      }
    }

    /* Distinctive Icon Colors */
    .icon-text-to-slug {
      background: linear-gradient(135deg, #2563eb, #1d4ed8);
      color: white;
    }

    .icon-lorem-ipsum {
      background: linear-gradient(135deg, #8B5CF6, #7C3AED);
      color: white;
    }

    .icon-case-converter {
      background: linear-gradient(135deg, #EC4899, #DB2777);
      color: white;
    }

    .icon-word-counter {
      background: linear-gradient(135deg, #10B981, #059669);
      color: white;
    }

    .icon-line-breaks {
      background: linear-gradient(135deg, #F59E0B, #D97706);
      color: white;
    }

    .icon-random-word {
      background: linear-gradient(135deg, #6366F1, #4F46E5);
      color: white;
    }

    .icon-privacy-policy {
      background: linear-gradient(135deg, #0EA5E9, #0284C7);
      color: white;
    }

    .icon-terms {
      background: linear-gradient(135deg, #4F46E5, #4338CA);
      color: white;
    }

    .icon-disclaimer {
      background: linear-gradient(135deg, #EF4444, #DC2626);
      color: white;
    }

    .icon-text-repeater {
      background: linear-gradient(135deg, #14B8A6, #0D9488);
      color: white;
    }

    .icon-text-sorter {
      background: linear-gradient(135deg, #8B5CF6, #7C3AED);
      color: white;
    }

    .icon-comma-separator {
      background: linear-gradient(135deg, #F97316, #EA580C);
      color: white;
    }

    .tool-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--text-color);
      margin-bottom: 8px;
    }

    .tool-description {
      color: var(--text-color-light);
      font-size: 14px;
      margin-bottom: 12px;
      line-height: 1.4;
    }

    .tool-link {
      display: inline-block;
      background: var(--primary-color);
      color: #ffffff !important;
      text-decoration: none;
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 600;
      font-size: 14px;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      border: 2px solid var(--primary-color);
      box-shadow: 0 2px 8px rgba(0, 71, 171, 0.2);
      position: relative;
      overflow: hidden;
    }

    .tool-link::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s ease;
    }

    @media (hover: hover) {
      .tool-link:hover {
        background: #003d96;
        border-color: #003d96;
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 8px 25px rgba(0, 71, 171, 0.4);
        color: #ffffff !important;
      }

      .tool-link:hover::before {
        left: 100%;
      }

      [data-theme="dark"] .tool-link:hover {
        background: #3b82f6;
        border-color: #3b82f6;
        box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
        color: #ffffff !important;
      }
    }

    [data-theme="dark"] .tool-link {
      background: #60a5fa;
      border-color: #60a5fa;
      color: #ffffff !important;
      box-shadow: 0 2px 8px rgba(96, 165, 250, 0.2);
    }

    /* Mobile: Remove ALL effects - Simple static cards */
    @media (max-width: 768px) {
      .container { padding: 6px 12px; }
      .page-header { margin-bottom: 16px; }
      .page-title { font-size: 28px; margin-bottom: 8px; }
      .page-description { font-size: 1rem; padding: 0 8px; }

      .tools-grid {
        display: block;
        padding: 8px 0;
      }

      /* Simple static cards - no effects */
      .tool-card {
        width: 100%;
        margin: 0 0 12px 0;
        padding: 16px;
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 10px;
        box-shadow: none;
        transition: none;
        transform: none;
        -webkit-tap-highlight-color: transparent;
      }

      /* Remove ALL hover effects */
      .tool-card:hover,
      .tool-card:active,
      .tool-card:focus {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        box-shadow: none;
        transform: none;
      }

      /* Remove pseudo-element effects */
      .tool-card::before,
      .tool-card::after {
        display: none;
      }

      /* Simple static icons */
      .tool-icon {
        width: 48px;
        height: 48px;
        margin: 0 auto 8px;
        font-size: 20px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: none;
        transform: none;
      }

      /* Remove icon effects */
      .tool-icon:hover,
      .tool-icon::before {
        transform: none;
        box-shadow: none;
      }

      /* Simple static buttons */
      .tool-link {
        display: inline-block;
        background: var(--primary-color);
        color: #ffffff;
        text-decoration: none;
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 600;
        font-size: 14px;
        border: none;
        box-shadow: none;
        transition: none;
        transform: none;
      }

      /* Remove button effects */
      .tool-link:hover,
      .tool-link:active,
      .tool-link:focus {
        background: var(--primary-color);
        color: #ffffff;
        box-shadow: none;
        transform: none;
      }

      .tool-link::before {
        display: none;
      }
    }
    @media (max-width: 480px) {
      .tool-card { min-width: 200px; max-width: 95vw; padding: 10px; }
      .tool-card .tool-icon { width: 40px; height: 40px; font-size: 18px; }
    }
    @media (max-width: 320px) {
      .tool-card { min-width: 140px; padding: 6px; }
      .tool-card .tool-icon { width: 32px; height: 32px; font-size: 15px; }
    }
  </style>
</head>
<body>


  <div class="container">
    <header class="page-header">
      <h1 id="main-title" class="page-title">Text Content Tools – Format, Convert &amp; Optimize Your Writing Online</h1>
      <p class="page-description">Format, convert, and optimize your content using our free text tools. Count words, generate slugs, sort or clean up text instantly — perfect for writers, bloggers, and SEO specialists.</p>
    </header>

    <main id="main-content" role="main" aria-labelledby="main-title">
      <section class="tools-section" aria-labelledby="tools-section-title">
        <h2 id="tools-section-title" class="sr-only">Available Text Tools</h2>
        <div class="tools-grid" role="list">
        <!-- Text to Slug -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-text-to-slug" aria-hidden="true">
            <i class="fas fa-link"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">Text to Slug</h3>
            <p class="tool-description">Convert text into URL-friendly slug format for clean URLs and SEO optimization.</p>
            <a class="tool-link" href="/p/text-to-slug_30.html" rel="noopener">Try this tool →</a>
          </div>
        </article>

        <!-- Lorem Ipsum Generator -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-lorem-ipsum" aria-hidden="true">
            <i class="fas fa-font"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">Lorem Ipsum Generator</h3>
            <p class="tool-description">Generate placeholder text for designs, layouts, and mockups with custom options.</p>
            <a class="tool-link" href="/p/lorem-ipsum-generator.html" rel="noopener">Try this tool →</a>
          </div>
        </article>

        <!-- Case Converter -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-case-converter" aria-hidden="true">
            <i class="fas fa-text-height"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">Case Converter</h3>
            <p class="tool-description">Convert text between uppercase, lowercase, title case, and sentence case formats.</p>
            <a class="tool-link" href="/p/case-converter.html" rel="noopener">Try this tool →</a>
          </div>
        </article>

        <!-- Word Counter -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-word-counter" aria-hidden="true">
            <i class="fas fa-calculator"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">Word Counter</h3>
            <p class="tool-description">Count words, characters, sentences, and paragraphs with detailed statistics.</p>
            <a class="tool-link" href="/p/word-counter.html" rel="noopener">Try this tool →</a>
          </div>
        </article>

        <!-- Remove Line Breaks -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-line-breaks" aria-hidden="true">
            <i class="fas fa-align-left"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">Remove Line Breaks</h3>
            <p class="tool-description">Remove unwanted line breaks and formatting issues to clean up your text content.</p>
            <a class="tool-link" href="/p/remove-line-breaks.html" rel="noopener">Try this tool →</a>
          </div>
        </article>

        <!-- Random Word Generator -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-random-word" aria-hidden="true">
            <i class="fas fa-random"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">Random Word Generator</h3>
            <p class="tool-description">Generate random words for creative writing, brainstorming, passwords, and more.</p>
            <a class="tool-link" href="/p/random-word-generator.html" rel="noopener">Try this tool →</a>
          </div>
        </article>

        <!-- Privacy Policy Generator -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-privacy-policy" aria-hidden="true">
            <i class="fas fa-shield-alt"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">Privacy Policy Generator</h3>
            <p class="tool-description">Create customized privacy policy for your website that complies with legal rules.</p>
            <a class="tool-link" href="/p/privacy-policy-generator.html" rel="noopener">Try this tool →</a>
          </div>
        </article>

        <!-- Terms And Conditions -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-terms" aria-hidden="true">
            <i class="fas fa-file-contract"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">Terms And Conditions</h3>
            <p class="tool-description">Generate comprehensive terms and conditions for your website, app, or service.</p>
            <a class="tool-link" href="/p/terms-and-condition-generator.html" rel="noopener">Try this tool →</a>
          </div>
        </article>

        <!-- Disclaimer Generator -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-disclaimer" aria-hidden="true">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">Disclaimer Generator</h3>
            <p class="tool-description">Create professional disclaimer to protect your website or business from legal issues.</p>
            <a class="tool-link" href="/p/disclaimer-generator.html" rel="noopener">Try this tool →</a>
          </div>
        </article>

        <!-- Text Repeater -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-text-repeater" aria-hidden="true">
            <i class="fas fa-copy"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">Text Repeater</h3>
            <p class="tool-description">Repeat any text or phrase multiple times with customizable separators and options.</p>
            <a class="tool-link" href="text-repeater.html" rel="noopener">Try this tool →</a>
          </div>
        </article>

        <!-- Text Sorter -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-text-sorter" aria-hidden="true">
            <i class="fas fa-sort-alpha-down"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">Text Sorter</h3>
            <p class="tool-description">Sort lines of text alphabetically, numerically, by length, or in reverse order.</p>
            <a class="tool-link" href="text-sorter.html" rel="noopener">Try this tool →</a>
          </div>
        </article>

        <!-- Comma Separator -->
        <article class="tool-card" role="listitem">
          <div class="tool-icon icon-comma-separator" aria-hidden="true">
            <i class="fas fa-list"></i>
          </div>
          <div class="tool-content">
            <h3 class="tool-title">Comma Separator</h3>
            <p class="tool-description">Add or remove commas from lists and convert between different list data formats.</p>
            <a class="tool-link" href="comma-separator.html" rel="noopener">Try this tool →</a>
          </div>
        </article>
        </div>
      </section>
    </main>
  </div>

  <script>
    // Simple: Desktop hover only, no mobile effects
    document.addEventListener('DOMContentLoaded', function() {
      // Only add hover effects on desktop
      if (window.innerWidth > 768) {
        const toolCards = document.querySelectorAll('.tool-card');
        toolCards.forEach(card => {
          card.addEventListener('mouseenter', function() {
            this.classList.add('show-description');
          });
          card.addEventListener('mouseleave', function() {
            this.classList.remove('show-description');
          });
        });
      }
      // Mobile: No JavaScript effects - pure static cards
    });
  </script>

</body>
</html>